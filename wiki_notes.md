# Ghi chú về files.py

## Tổng quan
File `files.py` là một script Python đơn giản được sử dụng để đọc và hiển thị dữ liệu từ file Excel.

## Chức năng chính

### 1. <PERSON><PERSON><PERSON> dữ liệu mẫu
- **M<PERSON><PERSON> đích**: Đọc 100 dòng đầu tiên từ file Excel
- **Phương thức**: Sử dụng `pd.read_excel()` với tham số `nrows=100`
- **Hiển thị**: In ra dữ liệu mẫu với tiêu đề "📊 Sample data:"

### 2. Đọc dữ liệu từ vị trí cụ thể
- **Mụ<PERSON> đích**: Đọc 100 dòng từ vị trí 5000-5100
- **Phương thức**: Sử dụng `pd.read_excel()` với:
  - `skiprows=4999`: Bỏ qua 4999 dòng đầu (để bắ<PERSON> đầu từ dòng 5000)
  - `nrows=100`: Đọc 100 dòng tiếp theo
- **Hiển thị**: In ra dữ liệu với tiêu đề "📊 Data from line 5000:"

## Thông tin kỹ thuật

### Dependencies
- **pandas**: Thư viện chính để xử lý dữ liệu Excel

### File đầu vào
- **Đường dẫn**: `C:\Users\<USER>\Taureau AI\genai - Test\cars_data_large.xlsx`
- **Loại file**: Excel (.xlsx)
- **Nội dung**: Dữ liệu về xe hơi (cars data)

### Xử lý lỗi
- Sử dụng `try-except` block để bắt và hiển thị lỗi

## Cách sử dụng
```python
python files.py
```

## Kết quả mong đợi
1. Hiển thị 100 dòng đầu tiên của dữ liệu xe hơi
2. Hiển thị 100 dòng từ vị trí 5000-5100
3. Nếu có lỗi, hiển thị thông báo lỗi chi tiết

## Ghi chú bổ sung
- Script này hữu ích để kiểm tra cấu trúc và nội dung của file Excel lớn
- Có thể được sử dụng để sampling dữ liệu từ các vị trí khác nhau trong dataset
- Phù hợp cho việc khám phá dữ liệu (data exploration) ban đầu

---
*Ghi chú được tạo ngày: 10/6/2025*
*File được phân tích: files.py*
